from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import httpx
import asyncio
import json
from datetime import datetime, timedelta
import logging
from fastapi.responses import StreamingResponse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger(__name__)

app = FastAPI()

# 全局变量存储token信息
global_token = {
    "api_token": None,
    "expires_at": None
}

# Token锁,避免并发刷新
token_lock = asyncio.Lock()

# 配置
COPILOT_CHAT_AUTH_URL = "https://api.github.com/copilot_internal/v2/token"
COPILOT_CHAT_COMPLETION_URL = "https://api.githubcopilot.com/chat/completions"
CONFIG_PATH = "/Users/<USER>/.config/github-copilot/hosts.json"

class Message(BaseModel):
    role: Optional[str] = None
    content: Optional[str] = None
    tool_calls: Optional[List[object]] = None
    tool_call_id: Optional[str] = None

class ChatCompletionRequest(BaseModel):
    messages: List[Message]
    model: Optional[str] = "claude-3.5-sonnet"
    temperature: Optional[float] = 0.0
    stream: Optional[bool] = False
    tools: Optional[List[object]] = None

async def get_oauth_token():
    try:
        with open(CONFIG_PATH, 'r') as file:
            config = json.load(file)
            return config.get("github.com", {}).get("oauth_token")
    except Exception as e:
        logger.error(f"Failed to get OAuth token: {e}")
        raise HTTPException(status_code=500, detail="Failed to get OAuth token")

async def refresh_api_token():
    oauth_token = await get_oauth_token()
    headers = {
        "User-Agent": "zed/0.169.0",
        "Authorization": f"token {oauth_token}",
        "Accept": "application/json"
    }

    async with httpx.AsyncClient() as client:
        response = await client.get(COPILOT_CHAT_AUTH_URL, headers=headers)
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail="Failed to refresh API token")

        token_info = response.json()
        return {
            "api_token": token_info['token'],
            "expires_at": datetime.utcfromtimestamp(token_info['expires_at'])
        }

async def get_valid_token():
    async with token_lock:
        if (global_token["api_token"] is None or
            global_token["expires_at"] is None or
            global_token["expires_at"] - datetime.utcnow() < timedelta(minutes=5)):

            token_info = await refresh_api_token()
            global_token.update(token_info)
            logger.info("API token refreshed")

        return global_token["api_token"]

async def stream_chat_completion(api_token: str, request: ChatCompletionRequest):
    headers = {
        "User-Agent": "zed/0.169.0",
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json",
        "Editor-Version": "Zed/0.169.0",
        "Copilot-Integration-Id": "vscode-chat",
        "Copilot-Vision-Request": "false"  # 假设不使用视觉请求
    }

#    request.model = "claude-3.5-sonnet"
    # request.model = "gpt-4o-2024-05-13"

    data = {
        "messages": [msg.model_dump() for msg in request.messages],
        "temperature": request.temperature,
        "model": request.model,
        "stream": request.stream,
        "tools": request.tools,
        "n": 1
    }

    # print data
    logger.info(f"Chat completion data: {data}")
    async with httpx.AsyncClient(timeout=None) as client:
        async with client.stream("POST", COPILOT_CHAT_COMPLETION_URL, headers=headers, json=data) as response:
            # 输出header x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset, x-ratelimit-used, x-ratelimit-resource
            logger.info(f"Response headers: {response.headers}")
            if response.status_code != 200:
                logger.error(f"Chat completion failed: {response.status_code}")
                raise HTTPException(status_code=response.status_code, detail="Chat completion failed")

            async for line in response.aiter_lines():
                if line:
                    yield line + "\n\n"

    # pub fn uses_streaming(&self) -> bool {
    #     match self {
    #         Self::Gpt4o | Self::Gpt4 | Self::Gpt3_5Turbo | Self::Claude3_5Sonnet => true,
    #         Self::O1Mini | Self::O1 => false,
    #     }
    # }

async def chat_completion(api_token: str, request: ChatCompletionRequest):
    headers = {
        "User-Agent": "zed/0.169.0",
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json",
        "Editor-Version": "Zed/0.169.0",
        "Copilot-Integration-Id": "vscode-chat",
        "Copilot-Vision-Request": "false"  # 假设不使用视觉请求
    }

    data = {
        "messages": [msg.model_dump() for msg in request.messages],
        "temperature": request.temperature,
        "model": request.model,
        "stream": request.stream,
        "tools": request.tools,
        "n": 1
    }

    # print data
    logger.info(f"Chat completion data: {data}")
    async with httpx.AsyncClient(timeout=300.0) as client:
        response = await client.post(COPILOT_CHAT_COMPLETION_URL, headers=headers, json=data)
        if response.status_code != 200:
            logger.error(f"Chat completion failed: {response.status_code} - {response.text}")
            raise HTTPException(status_code=response.status_code, detail="Chat completion failed")

        return response.json()

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    try:
        api_token = await get_valid_token()
        # log request temperature and model and stream
        logger.info(f"Chat completion request: {request.temperature}, {request.model}, {request.stream}")
        if request.stream:
            return StreamingResponse(stream_chat_completion(api_token, request), media_type="text/event-stream")
        else:
            response = await chat_completion(api_token, request)
            return response

    except Exception as e:
        logger.error(f"Chat completion error: {e}", exc_info=True)  # 添加exc_info=True以记录完整的堆栈跟踪
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
