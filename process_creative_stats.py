import csv
import requests
import warnings
import traceback
from time import sleep

# 禁用SSL警告
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

def call_backdoor_service(timestamp, creative_id):
    url = "https://cm.bilibili.co/report/api/backdoor"
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "max-age=0",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded",
        "Cookie": "buvid4=7E1B1828-2D5D-2C0A-F701-9C788170D2E622215-022012414-YwaIv4Va3kg02LA%2B6esLcA%3D%3D; _gitlab_token=************************************; logged_out_marketing_header_id=eyJfcmFpbHMiOnsibWVzc2FnZSI6IkltWTVaamxqTlRBNExUSm1OekF0TkRabFl5MWhaR1ptTFRBeVpESXdaR00zTmpVNU5DST0iLCJleHAiOm51bGwsInB1ciI6ImNvb2tpZS5sb2dnZWRfb3V0X21hcmtldGluZ19oZWFkZXJfaWQifX0%3D--b37dc5b5358ad3738c76327b08a03bb87ad3f5e0; buvid_fp=090ad0fb60612da31e00fe91d2f9799a; buvid3=5299ECF9-3313-0A86-85D9-D1FBEE44DA5660327infoc; b_nut=1733976060; fp=664703377cd99634a36d350fe2f009ee; uid=9399; mng-go=62a6d76f3af5c0154d5267a603f52f7244974617652e3d2966db23a59b8c8130; _AJSESSIONID=fa8e4e8992694cea083e92580df3c2ac; username=lizhenwei; _cm=eyJhbGciOiJIUzI1NiJ9.eyJwcm94eU5hbWUiOiJsaXpoZW53ZWkiLCJpc3MiOiLogYzlrabmlZnogrItQVNVLU1FQ1QtYmlsaWJpbGktMDQt5bim6LSn6LW36aOeIiwidHlwZSI6MTAwLCJleHAiOjE3NDg2MjcxNTcsImlhdCI6MTc0ODAyMjM1NywianRpIjoiMzIxMTk5MSIsInByb3h5SWQiOjB9.Fj5AkjCD1hTqR1xIei99JhsFk09Tbw-Es7dQkPlcjt4",
        "Origin": "http://cm-mng.bilibili.co",
        "Referer": "http://cm-mng.bilibili.co/manager/api/backdoor_v2",
        "Upgrade-Insecure-Requests": "1",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    }
    
    # 构造调用参数，直接使用传入的时间戳和创意ID数组
    expression = f'creativeStatService.insertToOcpcConversionLogDayFromHourEsNew({timestamp}, [{creative_id}])'
    data = {
        "expression": expression
    }
    
    print(f"调用表达式: {expression}")
    try:
        response = requests.post(url, headers=headers, data=data, verify=False)
        print(f"调用结果: {response.text}")
        return response.text
    except Exception as e:
        print(f"调用出错: {str(e)}")
        traceback.print_exc()
        return None

def main():
    try:
        # 打开CSV文件
        with open("/Users/<USER>/Downloads/data_day_converted.csv", "r") as csv_file:
            csv_reader = csv.reader(csv_file)
            # 读取所有行，不跳过标题行，假设直接是数据
            rows = list(csv_reader)
            
            print(f"读取到 {len(rows)} 行数据")
            
            # 假设CSV的两列分别是timestamp和creativeId
            for row in rows:
                if len(row) >= 2:
                    timestamp = row[0].strip()
                    creative_id = row[1].strip()
                    
                    print(f"处理数据: timestamp={timestamp}, creative_id={creative_id}")
                    
                    # 直接使用原始时间戳调用服务
                    result = call_backdoor_service(timestamp, creative_id)
                    
                    # 每次调用间隔1秒
                    print("等待1秒...")
                    sleep(0.1)
    except Exception as e:
        print(f"发生错误: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
